# 私人日記 APP 系統架構

## 1. 簡介

本文件闡述了「私人日記 APP」的完整全端架構，涵蓋後端系統、前端實作以及兩者之間的整合。它將作為 AI 驅動開發的單一事實來源，確保整個技術堆疊的一致性。

## 2. 高階架構

### 2.1 技術摘要

本專案將採用現代化的 Jamstack 架構，前端使用 React (Next.js) 框架進行開發，後端則採用 Node.js 搭配 Express 框架提供 API 服務。前後端將透過 RESTful API 進行通訊。資料庫選用 PostgreSQL，並部署在雲端平台上以實現高可用性和可擴展性。

### 2.2 平台與基礎設施

*   **平台:** Vercel + Heroku
*   **關鍵服務:**
    *   **Vercel:** 用於託管 Next.js 前端應用程式，提供 CDN 和自動化部署。
    *   **Heroku:** 用於託管 Node.js 後端 API 服務。
    *   **Heroku Postgres:** 作為主要的關聯式資料庫。
*   **部署區域:** US East

### 2.3 儲存庫結構

*   **結構:** Monorepo (單一儲存庫)
*   **Monorepo 工具:** npm workspaces
*   **套件組織:**
    *   `apps/web`: Next.js 前端應用程式
    *   `apps/api`: Express 後端 API
    *   `packages/ui`: 共用的 React 元件庫
    *   `packages/config`: 共用的設定檔 (ESLint, Prettier, TypeScript)

### 2.4 高階架構圖

```mermaid
graph TD
    A[使用者] --> B{瀏覽器};
    B --> C[Vercel (Next.js)];
    C --> D[Heroku (Node.js API)];
    D --> E[Heroku Postgres];
```

### 2.5 架構模式

*   **Jamstack:** 透過預先渲染靜態頁面並透過 API 存取動態內容，提供卓越的效能和安全性。
*   **元件化 UI:** 使用 React 元件來建構可重複使用且易於維護的使用者介面。
*   **RESTful API:** 作為前後端之間標準化的通訊介面。
*   **儲存庫模式 (Repository Pattern):** 抽象化資料存取邏輯，使業務邏輯與資料庫分離。

## 3. 技術堆疊

| 類別 | 技術 | 版本 | 用途 | 理由 |
| --- | --- | --- | --- | --- |
| 前端語言 | TypeScript | 5.x | 型別安全 | 提升程式碼品質與可維護性 |
| 前端框架 | Next.js | 14.x | React 框架 | 提供伺服器端渲染 (SSR) 和靜態網站生成 (SSG) |
| UI 元件庫 | Shadcn/ui | latest | UI 元件 | 提供一組美觀且可客製化的元件 |
| 狀態管理 | Zustand | 4.x | 狀態管理 | 輕量級且易於使用的狀態管理函式庫 |
| 後端語言 | TypeScript | 5.x | 型別安全 | 提升程式碼品質與可維護性 |
| 後端框架 | Express | 4.x | API 開發 | 成熟穩定，社群支援廣泛 |
| API 風格 | REST | N/A | 標準化通訊 | 簡單易懂，廣泛應用 |
| 資料庫 | PostgreSQL | 15.x | 主要資料庫 | 功能強大且可靠的開源關聯式資料庫 |
| 認證 | JWT | N/A | 使用者認證 | 無狀態認證，易於擴展 |
| 前端測試 | Jest + React Testing Library | latest | 單元/整合測試 | React 官方推薦的測試工具 |
| 後端測試 | Jest + Supertest | latest | API 測試 | 易於測試 Express API 端點 |
| CI/CD | GitHub Actions | N/A | 自動化建置與部署 | 與 GitHub 整合良好，設定簡單 |

## 4. 資料模型

### User

*   **用途:** 儲存使用者帳號資訊。
*   **屬性:**
    *   `id`: UUID (Primary Key)
    *   `email`: String (Unique)
    *   `password`: String (Hashed)
    *   `createdAt`: DateTime
    *   `updatedAt`: DateTime

### Diary

*   **用途:** 儲存日記內容。
*   **屬性:**
    *   `id`: UUID (Primary Key)
    *   `title`: String
    *   `content`: Text
    *   `date`: Date
    *   `userId`: UUID (Foreign Key to User)
    *   `createdAt`: DateTime
    *   `updatedAt`: DateTime

### Tag

*   **用途:** 儲存日記標籤。
*   **屬性:**
    *   `id`: UUID (Primary Key)
    *   `name`: String (Unique)
    *   `createdAt`: DateTime
    *   `updatedAt`: DateTime

### DiaryTag (關聯表)

*   **用途:** 建立 Diary 和 Tag 之間的多對多關聯。
*   **屬性:**
    *   `diaryId`: UUID (Foreign Key to Diary)
    *   `tagId`: UUID (Foreign Key to Tag)