# 私人日記 APP 產品需求文件 (PRD)

## 1. 目標與背景

*   **目標:** 開發一款支援多使用者的私人日記應用程式，讓使用者可以安全地記錄、儲存和管理他們的個人日記。
*   **背景:** 現代人生活繁忙，需要一個簡單、安全且私密的空間來記錄思緒、抒發情感。此應用程式旨在滿足此需求，並提供個人化的日記體驗。

## 2. 需求

### 2.1 功能性需求 (FR)

1.  **FR1:** 使用者可以使用電子郵件和密碼註冊帳號。
2.  **FR2:** 使用者可以使用電子郵件和密碼登入。
3.  **FR3:** 使用者可以建立、編輯和刪除日記。
4.  **FR4:** 每篇日記應包含標題、內容和日期。
5.  **FR5:** 使用者只能查看和編輯自己的日記。
6.  **FR6:** 應用程式應提供一個儀表板，顯示使用者所有日記的列表。
7.  **FR7:** 使用者可以為日記加上標籤，以便分類和搜尋。
8.  **FR8:** 使用者可以依關鍵字或標籤搜尋日記。

### 2.2 非功能性需求 (NFR)

1.  **NFR1:** 所有使用者資料，特別是日記內容，必須在傳輸和儲存時加密。
2.  **NFR2:** 應用程式應在 2 秒內載入。
3.  **NFR3:** 系統應能處理至少 100 個並發使用者。
4.  **NFR4:** 應用程式應與主流的網頁瀏覽器 (Chrome, Firefox, Safari) 相容。
5.  **NFR5:** 應用程式的使用者介面應直觀且易於使用。

## 3. Epics

*   **Epic 1: 基礎建設與使用者認證**
    *   **目標:** 建立專案基本架構、設定資料庫，並實現使用者註冊和登入功能。
*   **Epic 2: 日記管理核心功能**
    *   **目標:** 實現日記的建立、讀取、更新和刪除 (CRUD) 功能。
*   **Epic 3: 日記組織與搜尋**
    *   **目標:** 新增標籤功能，並允許使用者透過關鍵字和標籤搜尋日記。

## 4. Stories

### Epic 1: 基礎建設與使用者認證

*   **Story 1.1: 使用者註冊**
    *   **As a** 新使用者,
    *   **I want** 能夠使用我的電子郵件和密碼註冊一個新帳號,
    *   **so that** 我可以開始使用這個應用程式。
    *   **驗收標準:**
        1.  使用者可以在註冊頁面輸入電子郵件和密碼。
        2.  系統會驗證電子郵件格式是否正確。
        3.  密碼長度必須至少為 8 個字元。
        4.  成功註冊後，使用者會被重新導向到登入頁面。
*   **Story 1.2: 使用者登入**
    *   **As a** 已註冊的使用者,
    *   **I want** 能夠使用我的電子郵件和密碼登入,
    *   **so that** 我可以存取我的日記。
    *   **驗收標準:**
        1.  使用者可以在登入頁面輸入電子郵件和密碼。
        2.  如果憑證正確，使用者會被導向到日記儀表板。
        3.  如果憑證錯誤，系統會顯示錯誤訊息。

### Epic 2: 日記管理核心功能

*   **Story 2.1: 建立日記**
    *   **As a** 已登入的使用者,
    *   **I want** 能夠建立一篇新的日記，包含標題和內容,
    *   **so that** 我可以記錄我的想法。
    *   **驗收標準:**
        1.  使用者可以在儀表板上找到「建立新日記」的按鈕。
        2.  點擊後，會出現一個表單，讓使用者輸入標題和內容。
        3.  儲存後，新的日記會出現在儀表板的日記列表中。
*   **Story 2.2: 查看日記列表**
    *   **As a** 已登入的使用者,
    *   **I want** 能夠在儀表板上看到我所有日記的列表,
    *   **so that** 我可以快速瀏覽我的日記。
    *   **驗收標準:**
        1.  儀表板會以列表形式顯示所有日記，每項包含標題和日期。
        2.  列表會依照日期由新到舊排序。
*   **Story 2.3: 編輯與刪除日記**
    *   **As a** 已登入的使用者,
    *   **I want** 能夠編輯或刪除我自己的日記,
    *   **so that** 我可以修改或移除不再需要的內容。
    *   **驗收標準:**
        1.  在日記旁邊有編輯和刪除的按鈕。
        2.  點擊編輯會進入編輯模式，允許修改標題和內容。
        3.  點擊刪除會跳出確認對話框，確認後才會刪除日記。

### Epic 3: 日記組織與搜尋

*   **Story 3.1: 為日記加上標籤**
    *   **As a** 已登入的使用者,
    *   **I want** 能夠為我的日記加上多個標籤,
    *   **so that** 我可以更好地組織我的日記。
    *   **驗收標準:**
        1.  在建立或編輯日記時，可以新增或移除標籤。
        2.  一篇日記可以有多個標籤。
        3.  日記下方會顯示其所有的標籤。
*   **Story 3.2: 搜尋日記**
    *   **As a** 已登入的使用者,
    *   **I want** 能夠透過關鍵字或標籤搜尋我的日記,
    *   **so that** 我可以快速找到特定的日記。
    *   **驗收標準:**
        1.  儀表板上有一個搜尋框。
        2.  輸入關鍵字後，會顯示標題或內容中包含該關鍵字的日記。
        3.  點擊標籤後，會顯示所有包含該標籤的日記。