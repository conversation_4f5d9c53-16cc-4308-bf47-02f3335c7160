# 私人日記 APP 使用者體驗 (UX) 規格

## 1. 使用者流程

```mermaid
graph TD
    A[開始] --> B{有帳號嗎?};
    B -- 否 --> C[註冊頁面];
    C --> D[輸入信箱/密碼];
    D --> E{註冊成功?};
    E -- 是 --> F[登入頁面];
    E -- 否 --> C;
    B -- 是 --> F;
    F --> G[輸入信箱/密碼];
    G --> H{登入成功?};
    H -- 是 --> I[日記儀表板];
    H -- 否 --> F;
    I --> J[查看日記列表];
    J --> K{操作};
    K -- 建立新日記 --> L[日記編輯頁];
    K -- 編輯日記 --> L;
    K -- 刪除日記 --> M[確認刪除];
    M -- 是 --> I;
    M -- 否 --> J;
    L --> N[輸入標題/內容/標籤];
    N --> O{儲存?};
    O -- 是 --> I;
    O -- 否 --> L;
    I --> P[登出];
    P --> F;
```

## 2. 線框圖 (Wireframes)

### 2.1 登入/註冊頁面

*   **佈局:** 垂直置中卡片式設計。
*   **元素:**
    *   應用程式 Logo
    *   標題 (登入 / 註冊)
    *   電子郵件輸入框
    *   密碼輸入框
    *   主要按鈕 (登入 / 註冊)
    *   切換連結 (還沒有帳號？ / 已經有帳號了？)

### 2.2 日記儀表板

*   **佈局:** 兩欄式佈局。
*   **左欄:**
    *   搜尋框
    *   「建立新日記」按鈕
    *   標籤列表 (可點擊篩選)
*   **右欄:**
    *   日記列表 (卡片式)
    *   每張卡片顯示：標題、日期、部分內容預覽、標籤。
    *   卡片右上角有「編輯」和「刪除」圖示按鈕。

### 2.3 日記編輯頁

*   **佈局:** 單欄式佈局。
*   **元素:**
    *   返回儀表板的連結
    *   標題輸入框
    *   內容編輯區 (支援 Markdown 的富文本編輯器)
    *   標籤輸入區 (可新增/移除標籤)
    *   「儲存」按鈕

## 3. 視覺設計指南

*   **色彩:**
    *   **主色:** `#4A90E2` (寧靜藍) - 用於按鈕、連結和重點提示。
    *   **輔色:** `#F5A623` (溫暖橘) - 用於次要操作或提示。
    *   **背景色:** `#F8F9FA` (淺灰色) - 提供舒適的閱讀背景。
    *   **文字顏色:** `#333333` (深灰色) - 確保良好的可讀性。
*   **字體:**
    *   **標題:** `Georgia`, `serif` - 營造經典、優雅的感覺。
    *   **內文:** `Lato`, `sans-serif` - 現代、清晰且易於閱讀。
*   **風格:**
    *   簡潔、現代、專注於內容。
    *   使用圓角和陰影來創造柔和的層次感。
    *   圖示使用簡潔的線條風格 (e.g., Feather Icons)。