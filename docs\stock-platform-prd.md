# 台股投資TOP10分析平台 Product Requirements Document (PRD)

## Goals and Background Context

### Goals
- 為上班族投資人提供科學化、系統化的台股選股工具，減少投資決策時間和提升投資品質
- 整合五種量化投資策略，提供多元化的投資分析角度，降低單一策略失效風險
- 建立每日自動更新的TOP10投資建議清單，包含個股和ETF
- 在開市時間提供即時價格更新，確保用戶掌握最新市況
- 完全使用免費數據源，打破付費投顧服務的門檻限制
- 建立可擴展的平台架構，為未來功能擴展和商業化奠定基礎

### Background Context
台灣有超過1,100萬股民，其中約70%為上班族投資人，他們面臨時間限制、資訊過載、專業知識門檻和情緒化交易等核心挑戰。現有解決方案如券商研報更新頻率低、財經媒體推薦主觀性強、付費投顧服務成本高昂，都無法有效解決上班族投資人的痛點。

隨著台股ETF數量快速增長至200+檔，投資選擇更加複雜，上班族投資人迫切需要一個整合多種投資策略的自動化選股平台。本專案將透過五種經典量化投資方法（價值投資、成長投資、QVM多因子、技術+籌碼面、ESG+財務），為用戶提供科學化的投資決策支援。

### Change Log
| 日期 | 版本 | 描述 | 作者 |
|------|------|------|------|
| 2025-01-08 | 1.0 | 初始PRD版本，基於專案簡報創建 | John (PM) |

## Requirements

### Functional
1. **FR1:** 系統能夠每日自動從免費API（Yahoo Finance、FinMind、TEJ開放資料）獲取台股個股和ETF的財務數據、技術指標和籌碼資料
2. **FR2:** 系統能夠實作五種量化投資策略算法：價值投資（Graham型）、成長投資（PEG型）、品質+價值+動量（QVM多因子）、技術面+籌碼面、ESG+財務
3. **FR3:** 系統能夠每日運算並產生五種策略各自的TOP10個股和ETF推薦清單
4. **FR4:** 系統能夠自動排除財務異常股票（連續2季虧損、負債比>70%、處置股、全額交割股）
5. **FR5:** 系統能夠在開市時間（9:00-13:30）提供即時股價更新，延遲不超過15分鐘
6. **FR6:** 用戶能夠註冊帳號並登入平台，設定個人偏好的投資策略
7. **FR7:** 用戶能夠查看五種策略的TOP10清單，包含股票代號、名稱、當前價格、推薦理由、風險等級
8. **FR8:** 系統能夠為每檔推薦股票提供簡單易懂的選股邏輯說明
9. **FR9:** 用戶能夠客製化首頁顯示內容，選擇優先關注的投資策略
10. **FR10:** 系統能夠確保單一產業在最終TOP10清單中的權重不超過20%

### Non Functional
1. **NFR1:** 系統首頁載入時間必須在3秒內完成
2. **NFR2:** API數據獲取成功率必須達到95%以上
3. **NFR3:** 系統必須支援同時1000名用戶在線使用
4. **NFR4:** 系統可用性必須達到99.5%
5. **NFR5:** 即時價格更新延遲必須控制在5秒內
6. **NFR6:** 平台必須支援響應式設計，相容Chrome 90+、Firefox 88+、Safari 14+、Edge 90+
7. **NFR7:** 所有投資相關內容必須包含免責聲明和風險警示
8. **NFR8:** 用戶資料必須符合個資法規範，採用HTTPS加密傳輸

## User Interface Design Goals

### Overall UX Vision
打造一個簡潔、專業且高效的投資分析平台，讓上班族用戶能在最短時間內獲得所需的投資資訊。介面設計強調「一目了然」的資訊呈現，避免複雜的操作流程，讓用戶能夠快速理解五種投資策略的推薦結果並做出決策。

### Key Interaction Paradigms
- **卡片式佈局**：五種投資策略以獨立卡片形式呈現，用戶可快速切換查看
- **即時更新指示**：開市時間顯示即時價格變動，用明顯的視覺提示（如顏色變化、動畫）
- **一鍵操作**：主要功能（查看TOP10、切換策略、個人化設定）都能在一次點擊內完成
- **響應式導航**：桌面版使用側邊欄導航，行動版使用底部標籤欄

### Core Screens and Views
- **首頁儀表板**：顯示用戶偏好策略的TOP10清單和市場概況
- **策略比較頁面**：並排顯示五種策略的推薦結果
- **個股詳情頁面**：顯示單一股票的詳細分析和推薦理由
- **用戶設定頁面**：管理個人偏好、通知設定和帳戶資訊
- **登入/註冊頁面**：簡潔的用戶認證流程

### Accessibility: WCAG AA
確保平台符合WCAG AA標準，包含適當的色彩對比度、鍵盤導航支援、螢幕閱讀器相容性，讓視覺障礙用戶也能順利使用投資分析功能。

### Branding
採用專業、可信賴的視覺風格，以深藍色和灰色為主色調，傳達穩重和專業感。使用清晰的字體和充足的留白空間，避免過於花俏的設計元素，讓用戶專注於投資資訊本身。

### Target Device and Platforms: Web Responsive
優先設計桌面版網頁體驗（考慮上班族主要在辦公室使用），同時確保在平板和手機上也有良好的瀏覽體驗。未來可擴展為原生行動應用程式。

## Technical Assumptions

### Repository Structure: Monorepo
採用單一程式碼庫架構，將前端、後端、數據處理服務統一管理，便於小團隊協作和版本控制。

### Service Architecture
採用微服務架構，將系統分為以下核心服務：
- **數據獲取服務**：負責從各API源獲取和清理財務數據
- **計算引擎服務**：執行五種投資策略的量化分析
- **用戶服務**：處理註冊、認證、個人化設定
- **通知服務**：處理即時價格更新和推播通知
- **Web應用服務**：提供用戶介面和API閘道

### Testing Requirements
實施完整的測試金字塔策略：
- **單元測試**：覆蓋所有投資策略算法和核心業務邏輯
- **整合測試**：驗證API數據獲取和服務間通訊
- **端到端測試**：測試完整的用戶工作流程
- **效能測試**：確保系統在高負載下的穩定性

### Additional Technical Assumptions and Requests
- **前端技術棧**：React.js + TypeScript + Next.js，提供SEO優化和伺服器端渲染
- **後端技術棧**：Python + FastAPI，便於金融數據處理和機器學習整合
- **資料庫**：PostgreSQL（主要資料庫）+ Redis（快取層和即時數據）
- **雲端基礎設施**：AWS或Google Cloud Platform，使用容器化部署（Docker + Kubernetes）
- **API整合**：Yahoo Finance、FinMind、TEJ開放資料、證交所公開資訊
- **監控與日誌**：實施完整的應用程式監控和錯誤追蹤
- **CI/CD流程**：自動化測試、建置和部署流程
- **安全性**：HTTPS加密、API rate limiting、用戶資料保護

## Epic List

**Epic 1: 基礎架構與數據獲取**
建立專案基礎設施、API整合和核心數據處理能力，同時提供基本的健康檢查和數據驗證功能

**Epic 2: 投資策略引擎**
實作五種量化投資策略算法，建立每日運算和TOP10清單生成能力

**Epic 3: 用戶管理與個人化**
建立用戶註冊、登入、偏好設定和基本個人化功能

**Epic 4: 前端介面與用戶體驗**
開發響應式網頁介面，提供策略清單展示、個股詳情和用戶互動功能

## Epic 1: 基礎架構與數據獲取

建立台股投資平台的核心技術基礎設施，包含專案架構設置、CI/CD流程、數據庫設計，以及與免費API的穩定整合。此Epic完成後，系統將具備從多個數據源獲取台股資料的能力，並提供基本的數據驗證和健康檢查功能，為後續的投資策略開發奠定堅實基礎。

### Story 1.1: 專案基礎架構設置
As a 開發者,
I want 建立完整的專案架構和開發環境,
so that 團隊可以開始進行協作開發並確保代碼品質。

**驗收標準:**
1. 建立Monorepo結構，包含前端、後端、數據處理服務的資料夾架構
2. 設置本地開發環境配置文件和詳細的環境設置說明文檔
3. 配置CI/CD流程，包含自動化測試和部署
4. 建立PostgreSQL和Redis資料庫連接
5. 實作基本的健康檢查API端點（/health）
6. 設置代碼品質檢查工具（linting, formatting）

### Story 1.2: 免費API整合與數據獲取
As a 系統管理員,
I want 系統能夠穩定地從多個免費API獲取台股數據,
so that 平台有可靠的數據來源支撐投資分析功能。

**驗收標準:**
1. 整合Yahoo Finance API，獲取即時股價和基本面數據
2. 整合FinMind API，獲取財務報表和技術指標
3. 整合TEJ開放資料，獲取ESG評級資訊
4. 整合證交所公開資訊，獲取籌碼面數據
5. 實作API rate limiting和錯誤處理機制
6. 建立數據獲取成功率監控（目標95%以上）
7. 設置數據獲取失敗時的重試機制

### Story 1.3: 數據清理與驗證
As a 數據分析師,
I want 系統能夠自動清理和驗證獲取的數據,
so that 後續的投資策略計算基於準確可靠的數據。

**驗收標準:**
1. 實作數據格式標準化處理（統一股票代號格式、日期格式等）
2. 建立數據完整性檢查（缺失值處理、異常值偵測）
3. 實作財務異常股票自動排除邏輯（連續虧損、高負債比、處置股）
4. 建立數據品質報告功能，記錄數據獲取和清理狀況
5. 設置數據更新時間戳記錄
6. 實作數據備份和恢復機制

### Story 1.4: 基礎監控與日誌系統
As a 系統管理員,
I want 系統具備完整的監控和日誌記錄能力,
so that 可以及時發現和解決系統問題。

**驗收標準:**
1. 實作應用程式日誌記錄（包含API調用、錯誤、效能指標）
2. 設置系統效能監控（CPU、記憶體、磁碟使用率）
3. 建立API回應時間監控（目標<2秒）
4. 實作錯誤追蹤和警報機制
5. 設置數據庫連接和查詢效能監控
6. 建立系統可用性監控儀表板
