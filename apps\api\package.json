{"name": "api", "version": "1.0.0", "private": true, "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint ."}, "dependencies": {"express": "^4.18.2", "jsonwebtoken": "^9.0.0", "pg": "^8.11.0", "reflect-metadata": "^0.1.13", "typeorm": "^0.3.17"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.4.8", "@types/pg": "^8.10.2", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6"}}